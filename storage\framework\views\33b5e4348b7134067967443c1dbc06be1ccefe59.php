<?php $__env->startSection('title', 'Auction Listings - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Auction Listings'); ?>
<?php $__env->startSection('page-subtitle', 'Manage auction events and listings. Create new auctions and track their performance.'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('auction-listing.create')); ?>" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="hidden lg:inline">Add Auction Listing</span>
        <span class="lg:hidden">Add</span>
    </a>
    <a href="<?php echo e(route('items.create')); ?>" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
        </svg>
        <span class="hidden lg:inline">Add Item</span>
        <span class="lg:hidden">Item</span>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Enhanced Filters Section -->
<div class="bg-white rounded-xl p-6 mb-6 shadow-sm border border-gray-100">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Filter Auction Listings</h3>
            <p class="text-sm text-gray-600">Search and filter auction events and listings</p>
        </div>
        
        <form id="filter" class="flex flex-col sm:flex-row gap-3" method="GET">
            <!-- Search Input -->
            <div class="min-w-0 flex-1 sm:min-w-[250px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Search</label>
                <div class="relative">
                    <input type="text" name="search" value="<?php echo e(request()->search); ?>" 
                           placeholder="Search auction listings..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200">
                    <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex items-end">
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors duration-200 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
            </div>

            <!-- Clear Filters Button -->
            <?php if(request()->search): ?>
            <div class="flex items-end">
                <a href="<?php echo e(route('auction-listing.index')); ?>"
                   class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear
                </a>
            </div>
            <?php endif; ?>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Listings</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($auctionTypes->count())); ?></p>
            </div>
            <div class="p-3 bg-primary-100 rounded-lg">
                <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Active Auctions</p>
                <p class="text-2xl font-bold text-green-600"><?php echo e($auctionTypes->where('date_to', '>=', now())->count()); ?></p>
            </div>
            <div class="p-3 bg-green-100 rounded-lg">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Completed</p>
                <p class="text-2xl font-bold text-gray-600"><?php echo e($auctionTypes->where('date_to', '<', now())->count()); ?></p>
            </div>
            <div class="p-3 bg-gray-100 rounded-lg">
                <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Items</p>
                <p class="text-2xl font-bold text-blue-600"><?php echo e($auctionTypes->sum(function($auction) { return $auction->items()->whereNull('closed_by')->count(); })); ?></p>
            </div>
            <div class="p-3 bg-blue-100 rounded-lg">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Table -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">All Auction Listings</h3>
                <p class="text-sm text-gray-600 mt-1">Manage your auction events and track their performance</p>
            </div>
            
            <!-- Export Options -->
            <div class="flex items-center space-x-2">
                <button onclick="exportTable('csv')" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export CSV
                </button>
                <button onclick="exportTable('excel')" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 js-datatable" id="auction-listings-table">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Auction Listing
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Available Items
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Start Date
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        End Date
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                    </th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__empty_1 = true; $__currentLoopData = $auctionTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auctionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                    <!-- Auction Listing Column -->
                    <td class="px-6 py-4">
                        <a href="<?php echo e(route('auction-listing.show', $auctionType)); ?>" class="flex items-center group">
                            <div class="flex-shrink-0 mr-4">
                                <div class="h-16 w-16 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                                    <?php if($auctionType->image): ?>
                                        <img class="h-full w-full object-cover group-hover:scale-105 transition-transform duration-200"
                                             src="<?php echo e($auctionType->image); ?>"
                                             alt="<?php echo e($auctionType->name); ?>"
                                             loading="lazy">
                                    <?php else: ?>
                                        <div class="h-full w-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200">
                                            <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="flex-grow min-w-0">
                                <h4 class="text-sm font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-200 truncate">
                                    <?php echo e($auctionType->name ?? 'Untitled Auction'); ?>

                                </h4>
                                <p class="text-xs text-gray-500 mt-1">
                                    ID: <?php echo e($auctionType->id); ?>

                                </p>
                            </div>
                        </a>
                    </td>

                    <!-- Available Items Column -->
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                <?php echo e(number_format($auctionType->items()->whereNull('closed_by')->count())); ?> items
                            </span>
                        </div>
                    </td>

                    <!-- Start Date Column -->
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">
                            <?php echo e(\Carbon\Carbon::parse($auctionType->date_from)->format('M d, Y')); ?>

                        </div>
                        <div class="text-xs text-gray-500">
                            <?php echo e(\Carbon\Carbon::parse($auctionType->date_from)->format('g:i A')); ?>

                        </div>
                    </td>

                    <!-- End Date Column -->
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">
                            <?php echo e(\Carbon\Carbon::parse($auctionType->date_to)->format('M d, Y')); ?>

                        </div>
                        <div class="text-xs text-gray-500">
                            <?php echo e(\Carbon\Carbon::parse($auctionType->date_to)->format('g:i A')); ?>

                        </div>
                    </td>

                    <!-- Status Column -->
                    <td class="px-6 py-4">
                        <?php
                            $now = now();
                            $startDate = \Carbon\Carbon::parse($auctionType->date_from);
                            $endDate = \Carbon\Carbon::parse($auctionType->date_to);
                            
                            if ($now < $startDate) {
                                $status = 'upcoming';
                                $statusText = 'Upcoming';
                                $statusClass = 'bg-yellow-100 text-yellow-800';
                            } elseif ($now >= $startDate && $now <= $endDate) {
                                $status = 'active';
                                $statusText = 'Active';
                                $statusClass = 'bg-green-100 text-green-800';
                            } else {
                                $status = 'completed';
                                $statusText = 'Completed';
                                $statusClass = 'bg-gray-100 text-gray-800';
                            }
                        ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($statusClass); ?>">
                            <?php echo e($statusText); ?>

                        </span>
                    </td>

                    <!-- Description Column -->
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 max-w-xs">
                            <span class="truncate block" title="<?php echo e($auctionType->description); ?>">
                                <?php echo e(Str::limit($auctionType->description ?? 'No description', 50)); ?>

                            </span>
                        </div>
                    </td>

                    <!-- Actions Column -->
                    <td class="px-6 py-4 text-center">
                        <div class="flex items-center justify-center space-x-2">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $auctionType)): ?>
                            <a href="<?php echo e(route('auction-listing.show', $auctionType)); ?>" 
                               class="inline-flex items-center p-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                               title="View Details">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auctionType)): ?>
                            <a href="<?php echo e(route('auction-listing.edit', $auctionType)); ?>" 
                               class="inline-flex items-center p-2 text-sm font-medium text-amber-600 bg-amber-50 rounded-lg hover:bg-amber-100 transition-colors duration-200"
                               title="Edit">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $auctionType)): ?>
                            <form action="<?php echo e(route('auction-listing.destroy', $auctionType)); ?>" method="POST" 
                                  onsubmit="return confirm('Are you sure you want to delete this auction listing? This action cannot be undone.')"
                                  class="inline-block">
                                <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                <button type="submit" 
                                        class="inline-flex items-center p-2 text-sm font-medium text-red-600 bg-red-50 rounded-lg hover:bg-red-100 transition-colors duration-200"
                                        title="Delete">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No auction listings found</h3>
                            <p class="text-gray-500 mb-4">Get started by creating your first auction listing.</p>
                            <a href="<?php echo e(route('auction-listing.create')); ?>" 
                               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200">
                                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create Auction Listing
                            </a>
                        </div>
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Initialize DataTable using the modernized module
    // ModernizedDataTable.init('.js-datatable', 'auction-listings-table', [0, 'asc']);

    // Enhanced table interactions
    enhanceTableInteractions();
    
    console.log('Modernized auction listings page loaded');
});

// Enhanced table interactions
function enhanceTableInteractions() {
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        // Add hover effects
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 104, 255, 0.1)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.boxShadow = '';
        });

        // Add click to view functionality
        const viewBtn = row.querySelector('a[href*="/auction-listing/"]');
        if (viewBtn) {
            row.style.cursor = 'pointer';
            row.addEventListener('click', function(e) {
                // Don't trigger if clicking on buttons or links
                if (e.target.closest('button, a, form')) return;

                viewBtn.click();
            });
        }
    });
}

// Export functionality
function exportTable(format) {
    const table = document.getElementById('auction-listings-table');
    const rows = table.querySelectorAll('tr');
    
    let csvContent = '';
    
    rows.forEach((row, index) => {
        const cols = row.querySelectorAll('th, td');
        const rowData = [];
        
        cols.forEach((col, colIndex) => {
            // Skip the actions column (last column)
            if (colIndex < cols.length - 1) {
                let cellText = col.textContent.trim();
                // Clean up the text
                cellText = cellText.replace(/\s+/g, ' ');
                rowData.push('"' + cellText.replace(/"/g, '""') + '"');
            }
        });
        
        if (rowData.length > 0) {
            csvContent += rowData.join(',') + '\n';
        }
    });
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `auction-listings-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/admin/modernized-auction-listing-index.blade.php ENDPATH**/ ?>